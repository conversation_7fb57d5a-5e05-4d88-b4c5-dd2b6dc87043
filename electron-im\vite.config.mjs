import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  base: '/zhuyuqian',
  plugins: [vue(), tailwindcss()],
  root: 'src/renderer',
  server: {
    host: '0.0.0.0',
    port: 3000,
    proxy: {
      '/zhuyuqian/api': {
        target: 'http://***********:3000',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/zhuyuqian\/api/, '/api')
      }
      // '/zhuyuqian/ws': {
      //   target: 'ws://***********:3000',
      //   changeOrigin: true,
      //   ws: true,
      //   rewrite: (path) => path.replace(/^\/zhuyuqian\/ws/, '/ws')
      // }
    }
  },
  resolve: {
    alias: {
      '@renderer': resolve('src/renderer/src'),
      '@': resolve('src/renderer/src')
    }
  }
})
